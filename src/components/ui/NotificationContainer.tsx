import { AnimatePresence } from 'framer-motion';
import React from 'react';

import { cn } from '@/lib/twMerge/cn';
import { NotificationContainerProps } from '@/types/notifications';

import NotificationToast from './NotificationToast';

/**
 * Container component for managing multiple notification toasts
 */
const NotificationContainer: React.FC<NotificationContainerProps> = ({
  notifications,
  onClose,
  className,
  maxNotifications = 5,
  size = 'large',
}) => {
  // Filter out notifications that are being removed and limit display count
  const visibleNotifications = notifications
    .filter(notification => !notification.isRemoving)
    .slice(0, maxNotifications);

  // Don't render container if no notifications
  if (visibleNotifications.length === 0) {
    return null;
  }

  return (
    <div
      className={cn(
        // Base container styles
        'w-full space-y-3',
        className
      )}
      role="region"
      aria-label="Notifications"
      aria-live="polite"
    >
      <AnimatePresence mode="popLayout">
        {visibleNotifications.map(notification => (
          <NotificationToast
            key={notification.id}
            notification={notification}
            onClose={onClose}
            className="w-full"
            size={size}
          />
        ))}
      </AnimatePresence>

      {/* Screen reader announcement for notification count */}
      <div className="sr-only" aria-live="polite">
        {visibleNotifications.length === 1
          ? '1 notification'
          : `${visibleNotifications.length} notifications`}
      </div>
    </div>
  );
};

export default NotificationContainer;
